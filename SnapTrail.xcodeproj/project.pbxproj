// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		493690C32E17BB1E00EBDA00 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 493690AA2E17BB1C00EBDA00 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 493690B12E17BB1C00EBDA00;
			remoteInfo = SnapTrail;
		};
		493690CD2E17BB1E00EBDA00 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 493690AA2E17BB1C00EBDA00 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 493690B12E17BB1C00EBDA00;
			remoteInfo = SnapTrail;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		493690B22E17BB1C00EBDA00 /* SnapTrail.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SnapTrail.app; sourceTree = BUILT_PRODUCTS_DIR; };
		493690C22E17BB1E00EBDA00 /* SnapTrailTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SnapTrailTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		493690CC2E17BB1E00EBDA00 /* SnapTrailUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SnapTrailUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		493690B42E17BB1C00EBDA00 /* SnapTrail */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SnapTrail;
			sourceTree = "<group>";
		};
		493690C52E17BB1E00EBDA00 /* SnapTrailTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SnapTrailTests;
			sourceTree = "<group>";
		};
		493690CF2E17BB1E00EBDA00 /* SnapTrailUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SnapTrailUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		493690AF2E17BB1C00EBDA00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690BF2E17BB1E00EBDA00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690C92E17BB1E00EBDA00 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		493690A92E17BB1C00EBDA00 = {
			isa = PBXGroup;
			children = (
				493690B42E17BB1C00EBDA00 /* SnapTrail */,
				493690C52E17BB1E00EBDA00 /* SnapTrailTests */,
				493690CF2E17BB1E00EBDA00 /* SnapTrailUITests */,
				493690B32E17BB1C00EBDA00 /* Products */,
			);
			sourceTree = "<group>";
		};
		493690B32E17BB1C00EBDA00 /* Products */ = {
			isa = PBXGroup;
			children = (
				493690B22E17BB1C00EBDA00 /* SnapTrail.app */,
				493690C22E17BB1E00EBDA00 /* SnapTrailTests.xctest */,
				493690CC2E17BB1E00EBDA00 /* SnapTrailUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		493690B12E17BB1C00EBDA00 /* SnapTrail */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 493690D62E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrail" */;
			buildPhases = (
				493690AE2E17BB1C00EBDA00 /* Sources */,
				493690AF2E17BB1C00EBDA00 /* Frameworks */,
				493690B02E17BB1C00EBDA00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				493690B42E17BB1C00EBDA00 /* SnapTrail */,
			);
			name = SnapTrail;
			packageProductDependencies = (
			);
			productName = SnapTrail;
			productReference = 493690B22E17BB1C00EBDA00 /* SnapTrail.app */;
			productType = "com.apple.product-type.application";
		};
		493690C12E17BB1E00EBDA00 /* SnapTrailTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 493690D92E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrailTests" */;
			buildPhases = (
				493690BE2E17BB1E00EBDA00 /* Sources */,
				493690BF2E17BB1E00EBDA00 /* Frameworks */,
				493690C02E17BB1E00EBDA00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				493690C42E17BB1E00EBDA00 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				493690C52E17BB1E00EBDA00 /* SnapTrailTests */,
			);
			name = SnapTrailTests;
			packageProductDependencies = (
			);
			productName = SnapTrailTests;
			productReference = 493690C22E17BB1E00EBDA00 /* SnapTrailTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		493690CB2E17BB1E00EBDA00 /* SnapTrailUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 493690DC2E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrailUITests" */;
			buildPhases = (
				493690C82E17BB1E00EBDA00 /* Sources */,
				493690C92E17BB1E00EBDA00 /* Frameworks */,
				493690CA2E17BB1E00EBDA00 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				493690CE2E17BB1E00EBDA00 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				493690CF2E17BB1E00EBDA00 /* SnapTrailUITests */,
			);
			name = SnapTrailUITests;
			packageProductDependencies = (
			);
			productName = SnapTrailUITests;
			productReference = 493690CC2E17BB1E00EBDA00 /* SnapTrailUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		493690AA2E17BB1C00EBDA00 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					493690B12E17BB1C00EBDA00 = {
						CreatedOnToolsVersion = 16.4;
					};
					493690C12E17BB1E00EBDA00 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 493690B12E17BB1C00EBDA00;
					};
					493690CB2E17BB1E00EBDA00 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 493690B12E17BB1C00EBDA00;
					};
				};
			};
			buildConfigurationList = 493690AD2E17BB1C00EBDA00 /* Build configuration list for PBXProject "SnapTrail" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 493690A92E17BB1C00EBDA00;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 493690B32E17BB1C00EBDA00 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				493690B12E17BB1C00EBDA00 /* SnapTrail */,
				493690C12E17BB1E00EBDA00 /* SnapTrailTests */,
				493690CB2E17BB1E00EBDA00 /* SnapTrailUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		493690B02E17BB1C00EBDA00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690C02E17BB1E00EBDA00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690CA2E17BB1E00EBDA00 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		493690AE2E17BB1C00EBDA00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690BE2E17BB1E00EBDA00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		493690C82E17BB1E00EBDA00 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		493690C42E17BB1E00EBDA00 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 493690B12E17BB1C00EBDA00 /* SnapTrail */;
			targetProxy = 493690C32E17BB1E00EBDA00 /* PBXContainerItemProxy */;
		};
		493690CE2E17BB1E00EBDA00 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 493690B12E17BB1C00EBDA00 /* SnapTrail */;
			targetProxy = 493690CD2E17BB1E00EBDA00 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		493690D42E17BB1E00EBDA00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		493690D52E17BB1E00EBDA00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		493690D72E17BB1E00EBDA00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SnapTrail/SnapTrail.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "SnapTrail需要访问您的位置信息来记录照片的拍摄地点";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "SnapTrail需要访问您的照片库来导入和管理您的照片";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrail;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		493690D82E17BB1E00EBDA00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SnapTrail/SnapTrail.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "SnapTrail需要访问您的位置信息来记录照片的拍摄地点";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "SnapTrail需要访问您的照片库来导入和管理您的照片";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrail;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		493690DA2E17BB1E00EBDA00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrailTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SnapTrail.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SnapTrail";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		493690DB2E17BB1E00EBDA00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrailTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SnapTrail.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SnapTrail";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		493690DD2E17BB1E00EBDA00 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrailUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = SnapTrail;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		493690DE2E17BB1E00EBDA00 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = syw741.SnapTrailUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = SnapTrail;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		493690AD2E17BB1C00EBDA00 /* Build configuration list for PBXProject "SnapTrail" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				493690D42E17BB1E00EBDA00 /* Debug */,
				493690D52E17BB1E00EBDA00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		493690D62E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrail" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				493690D72E17BB1E00EBDA00 /* Debug */,
				493690D82E17BB1E00EBDA00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		493690D92E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrailTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				493690DA2E17BB1E00EBDA00 /* Debug */,
				493690DB2E17BB1E00EBDA00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		493690DC2E17BB1E00EBDA00 /* Build configuration list for PBXNativeTarget "SnapTrailUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				493690DD2E17BB1E00EBDA00 /* Debug */,
				493690DE2E17BB1E00EBDA00 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 493690AA2E17BB1C00EBDA00 /* Project object */;
}
