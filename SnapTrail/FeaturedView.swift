//
//  FeaturedView.swift
//  SnapTrail
//
//  Created by 刘坤和 on 2025/7/4.
//

import SwiftUI
import SwiftData
import Photos

struct FeaturedView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var photos: [Photo]
    @State private var selectedPhotos: [PHAsset] = []
    @State private var showingPhotoPicker = false

    var body: some View {
        NavigationView {
            VStack {
                if photos.isEmpty {
                    // 照片网格占位符
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            VStack {
                                Image(systemName: "photo.on.rectangle.angled")
                                    .font(.system(size: 50))
                                    .foregroundColor(.gray)
                                Text("精选照片")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("点击右上角的 + 按钮添加照片")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        )
                } else {
                    // 照片网格
                    ScrollView {
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(photos, id: \.id) { photo in
                                PhotoThumbnailView(photo: photo)
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("精选")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingPhotoPicker = true
                    }) {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingPhotoPicker) {
                PhotoPicker(selectedPhotos: $selectedPhotos)
            }
            .onChange(of: selectedPhotos) { _, newPhotos in
                savePhotos(newPhotos)
            }
        }
    }

    private func savePhotos(_ assets: [PHAsset]) {
        for asset in assets {
            let metadata = PhotoMetadataExtractor.extractMetadata(from: asset)

            let photo = Photo(
                originalAssetIdentifier: asset.localIdentifier,
                captureDate: metadata.date ?? Date(),
                location: metadata.location
            )

            modelContext.insert(photo)
        }

        do {
            try modelContext.save()
        } catch {
            print("保存照片失败: \(error)")
        }

        // 清空选中的照片
        selectedPhotos.removeAll()
    }
}

#Preview {
    FeaturedView()
}
