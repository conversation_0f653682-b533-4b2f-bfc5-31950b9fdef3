//
//  MapView.swift
//  SnapTrail
//
//  Created by 刘坤和 on 2025/7/4.
//

import SwiftUI
import MapKit
import SwiftData

struct MapView: View {
    @Query private var photos: [Photo]
    @State private var selectedPhoto: Photo?
    @State private var showingPhotoDetail = false
    @State private var cameraPosition = MapCameraPosition.automatic

    // 过滤有位置信息的照片
    private var photosWithLocation: [Photo] {
        photos.filter { $0.location != nil }
    }

    var body: some View {
        NavigationView {
            VStack {
                if photosWithLocation.isEmpty {
                    // 地图占位符
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            VStack {
                                Image(systemName: "map")
                                    .font(.system(size: 50))
                                    .foregroundColor(.gray)
                                Text("地图视图")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("添加带有位置信息的照片后，它们将在这里显示")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.center)
                                    .padding()
                            }
                        )
                } else {
                    // 实际地图
                    Map(position: $cameraPosition) {
                        ForEach(photosWithLocation, id: \.id) { photo in
                            if let location = photo.location {
                                Annotation("", coordinate: location.coordinate) {
                                    Button(action: {
                                        selectedPhoto = photo
                                        showingPhotoDetail = true
                                    }) {
                                        Image(systemName: "photo.circle.fill")
                                            .font(.title2)
                                            .foregroundColor(.blue)
                                            .background(Color.white)
                                            .clipShape(Circle())
                                            .shadow(radius: 3)
                                    }
                                }
                            }
                        }
                    }
                    .mapStyle(.standard)
                }
            }
            .navigationTitle("地图")
            .sheet(isPresented: $showingPhotoDetail) {
                if let photo = selectedPhoto {
                    PhotoDetailView(photo: photo)
                }
            }
        }
    }
}

#Preview {
    MapView()
}
