//
//  PhotoDetailView.swift
//  SnapTrail
//
//  Created by 刘坤和 on 2025/7/4.
//

import SwiftUI
import Photos

struct PhotoDetailView: View {
    let photo: Photo
    @Environment(\.dismiss) private var dismiss
    @State private var photoImage: UIImage?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 照片预览
                if let image = photoImage {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 400)
                        .cornerRadius(12)
                        .shadow(radius: 5)
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 400)
                        .overlay(
                            VStack {
                                ProgressView()
                                    .scaleEffect(1.5)
                                Text("加载中...")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .padding(.top)
                            }
                        )
                        .cornerRadius(12)
                }
                
                // 照片信息
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(.blue)
                        Text("拍摄时间")
                            .font(.headline)
                        Spacer()
                        Text(photo.captureDate, style: .date)
                            .font(.body)
                    }
                    
                    if let location = photo.location {
                        HStack {
                            Image(systemName: "location")
                                .foregroundColor(.green)
                            Text("拍摄地点")
                                .font(.headline)
                            Spacer()
                            VStack(alignment: .trailing) {
                                Text("纬度: \(location.coordinate.latitude, specifier: "%.6f")")
                                    .font(.caption)
                                Text("经度: \(location.coordinate.longitude, specifier: "%.6f")")
                                    .font(.caption)
                            }
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationTitle("照片详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadPhotoImage()
        }
    }
    
    private func loadPhotoImage() {
        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [photo.originalAssetIdentifier], options: nil)
        guard let asset = fetchResult.firstObject else { return }
        
        let manager = PHImageManager.default()
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        
        manager.requestImage(
            for: asset,
            targetSize: CGSize(width: 800, height: 800),
            contentMode: .aspectFit,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                self.photoImage = image
            }
        }
    }
}

#Preview {
    PhotoDetailView(photo: Photo(
        originalAssetIdentifier: "test",
        captureDate: Date(),
        latitude: 37.7749,
        longitude: -122.4194
    ))
}
