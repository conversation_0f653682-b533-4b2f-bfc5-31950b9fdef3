//
//  Photo.swift
//  SnapTrail
//
//  Created by 刘坤和 on 2025/7/4.
//

import Foundation
import SwiftData
import CoreLocation

@Model
final class Photo {
    var id: UUID
    var originalAssetIdentifier: String
    var captureDate: Date
    var latitude: Double?
    var longitude: Double?
    
    // 计算属性，用于获取 CLLocation 对象
    var location: CLLocation? {
        guard let latitude = latitude, let longitude = longitude else {
            return nil
        }
        return CLLocation(latitude: latitude, longitude: longitude)
    }
    
    init(id: UUID = UUID(), originalAssetIdentifier: String, captureDate: Date, latitude: Double? = nil, longitude: Double? = nil) {
        self.id = id
        self.originalAssetIdentifier = originalAssetIdentifier
        self.captureDate = captureDate
        self.latitude = latitude
        self.longitude = longitude
    }
    
    // 便利初始化器，接受 CLLocation 参数
    convenience init(id: UUID = UUID(), originalAssetIdentifier: String, captureDate: Date, location: CLLocation?) {
        self.init(
            id: id,
            originalAssetIdentifier: originalAssetIdentifier,
            captureDate: captureDate,
            latitude: location?.coordinate.latitude,
            longitude: location?.coordinate.longitude
        )
    }
}
