//
//  PhotoThumbnailView.swift
//  SnapTrail
//
//  Created by 刘坤和 on 2025/7/4.
//

import SwiftUI
import Photos

struct PhotoThumbnailView: View {
    let photo: Photo
    @State private var thumbnailImage: UIImage?
    @State private var showingDetail = false
    
    var body: some View {
        Button(action: {
            showingDetail = true
        }) {
            Group {
                if let image = thumbnailImage {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                }
            }
            .frame(width: 120, height: 120)
            .clipped()
            .cornerRadius(8)
            .overlay(
                // 位置标识
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        if photo.location != nil {
                            Image(systemName: "location.fill")
                                .font(.caption2)
                                .foregroundColor(.white)
                                .padding(4)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                    }
                }
                .padding(4)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onAppear {
            loadThumbnail()
        }
        .sheet(isPresented: $showingDetail) {
            PhotoDetailView(photo: photo)
        }
    }
    
    private func loadThumbnail() {
        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [photo.originalAssetIdentifier], options: nil)
        guard let asset = fetchResult.firstObject else { return }
        
        let manager = PHImageManager.default()
        let options = PHImageRequestOptions()
        options.deliveryMode = .fastFormat
        options.isNetworkAccessAllowed = true
        
        manager.requestImage(
            for: asset,
            targetSize: CGSize(width: 240, height: 240),
            contentMode: .aspectFill,
            options: options
        ) { image, _ in
            DispatchQueue.main.async {
                self.thumbnailImage = image
            }
        }
    }
}

#Preview {
    PhotoThumbnailView(photo: Photo(
        originalAssetIdentifier: "test",
        captureDate: Date(),
        latitude: 37.7749,
        longitude: -122.4194
    ))
}
